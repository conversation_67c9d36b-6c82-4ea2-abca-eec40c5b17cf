# 阿米巴ERP系统逆向工程方法论手册 v1.0

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-06-27
- **创建者**: 逆向工程主管 + 逆向工程专家
- **适用项目**: 阿米巴ERP系统复刻项目第一阶段

## 1. 逆向工程策略概述

### 1.1 目标系统信息
- **系统名称**: 人单云（阿米巴ERP系统）
- **系统地址**: https://app.huoban.com/navigations/3300000036684424/pages/7000000001717847
- **登录信息**: 用户名 15313656268，密码 huoban123
- **技术基础**: 基于"伙伴云"低代码平台构建
- **分析范围**: 仅关注阿米巴ERP系统功能，不涉及伙伴云基础平台

### 1.2 逆向工程方法论原则
1. **黑盒分析为主**: 通过系统外部行为推断内部逻辑
2. **结构化分解**: 按功能域、用户角色、业务流程系统化分析
3. **多维度验证**: 通过不同角度交叉验证分析结果
4. **知识产权合规**: 严格遵守法律法规，仅进行功能分析
5. **文档化记录**: 所有发现必须详细记录并可追溯

### 1.3 分析技术组合
- **界面遍历分析**: 系统化访问所有功能入口
- **用户行为模拟**: 模拟不同角色的典型操作流程
- **数据流跟踪**: 观察数据在系统中的流转路径
- **状态转换分析**: 记录系统状态变化规律
- **性能特征测量**: 量化系统响应和处理能力

## 2. 逆向分析工具链配置

### 2.1 主要分析工具
1. **Playwright MCP工具**
   - 用途: 自动化浏览器操作，系统功能遍历
   - 配置状态: 已测试可用
   - 使用场景: 界面截图、操作录制、数据提取

2. **屏幕录制系统**
   - 工具: 系统内置录屏功能
   - 用途: 记录完整操作流程
   - 输出格式: MP4视频文件

3. **文档管理系统**
   - 工具: Markdown文档 + Git版本控制
   - 用途: 结构化记录分析发现
   - 存储位置: 项目根目录对应模块文件夹

### 2.2 数据采集框架
```
数据采集层次结构:
├── 原始数据层
│   ├── 界面截图
│   ├── 操作录屏
│   └── 系统响应日志
├── 结构化数据层
│   ├── 功能清单
│   ├── 业务规则库
│   └── 用户角色矩阵
└── 分析成果层
    ├── 功能地图
    ├── 业务流程模型
    └── 需求规格文档
```

## 3. 知识获取计划

### 3.1 系统性功能发现策略
1. **广度优先遍历**: 先识别所有一级功能模块
2. **深度功能挖掘**: 逐个模块深入分析子功能
3. **角色切换验证**: 不同用户角色下的功能差异分析
4. **边界条件测试**: 测试系统功能边界和异常处理

### 3.2 隐性知识获取机制
1. **专家访谈计划**
   - 目标: 获取未文档化的业务规则和使用技巧
   - 方法: 结构化访谈 + 实际操作演示
   - 记录: 音频记录 + 要点整理

2. **用户观察方案**
   - 目标: 发现真实使用模式和习惯
   - 方法: 用户操作全程观察记录
   - 分析: 操作路径分析 + 效率评估

### 3.3 业务规则提取模板
```markdown
## 业务规则记录模板
- **规则ID**: BR-XXX
- **规则名称**: [规则简要描述]
- **触发条件**: [什么情况下规则生效]
- **规则逻辑**: [具体的业务逻辑]
- **执行结果**: [规则执行后的系统行为]
- **异常处理**: [规则失败时的处理方式]
- **发现来源**: [通过什么方式发现此规则]
- **验证状态**: [已验证/待验证/存疑]
```

## 4. 质量保证机制

### 4.1 分析质量标准
- **完整性**: 功能覆盖率 ≥ 95%
- **准确性**: 业务规则描述准确率 ≥ 90%
- **一致性**: 不同分析维度结果一致性 ≥ 95%
- **可验证性**: 所有发现都有明确的验证路径

### 4.2 质量控制流程
1. **同行评审**: 每个分析成果都需要其他专家审核
2. **交叉验证**: 通过不同方法验证同一发现
3. **用户确认**: 关键业务规则需要业务专家确认
4. **版本控制**: 所有文档都进行版本管理和变更跟踪

## 5. 风险管理与合规保障

### 5.1 知识产权合规原则
1. **仅进行功能分析**: 不涉及代码逆向或技术实现细节
2. **公开信息为主**: 主要通过用户界面和公开功能进行分析
3. **合法访问**: 使用合法账户和正常操作方式
4. **成果独立**: 分析成果为独立创作，不复制原系统代码

### 5.2 风险识别与应对
1. **功能遗漏风险**
   - 应对: 建立多层验证机制，用户确认 + 交叉检查
   - 预警: 关键用户对功能地图确认率 < 90%

2. **理解偏差风险**
   - 应对: 增加专家访谈，扩大观察样本
   - 验证: 业务规则准确率监控

3. **时间进度风险**
   - 应对: 并行工作模式，关键路径管理
   - 监控: 每周进度评估和调整

## 6. 工作标准与规范

### 6.1 文档命名规范
```
文档类型_模块名称_版本号.扩展名
示例: 功能地图_用户管理模块_v1.0.md
```

### 6.2 分析记录标准
- 每个功能点必须包含: 功能名称、入口路径、操作步骤、预期结果
- 每个业务规则必须包含: 规则描述、触发条件、执行逻辑、异常处理
- 每个用户角色必须包含: 角色定义、权限范围、典型场景、操作限制

### 6.3 成果交付标准
- 所有文档必须经过同行评审
- 关键发现必须有截图或录屏证据
- 分析结论必须有明确的推理过程
- 交付物必须通过质量检查清单验证

## 7. 实施计划

### 7.1 环境准备阶段 (当前)
- [x] 制定逆向工程方法论
- [x] 配置Playwright MCP工具
- [ ] 建立文档管理体系
- [ ] 设置质量控制流程

### 7.2 工具验证阶段 (下一步)
- [ ] 验证Playwright工具功能完整性
- [ ] 测试目标系统访问稳定性
- [ ] 建立分析数据存储结构
- [ ] 制定详细操作手册

### 7.3 正式分析准备 (后续)
- [ ] 团队培训和角色分工
- [ ] 建立协作机制
- [ ] 启动系统性功能发现
- [ ] 开始专家知识获取

---

**文档状态**: 初版完成，待环境验证后更新
**下一步行动**: 验证Playwright工具，建立文档管理体系
