# 逆向工程工具链使用指南 v1.0

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-06-27
- **创建者**: 工程工具专家 + 逆向工程专家
- **适用范围**: 阿米巴ERP系统逆向分析项目

## 1. 工具链架构概述

### 1.1 核心工具组件
```
逆向工程工具链架构:
├── 自动化分析层
│   ├── Playwright MCP工具 (主要)
│   ├── 浏览器自动化引擎
│   └── 脚本执行环境
├── 数据采集层
│   ├── 截图采集工具
│   ├── 文本提取工具
│   ├── HTML结构分析工具
│   └── 网络请求监控工具
├── 数据处理层
│   ├── 图像处理工具
│   ├── 文本分析工具
│   ├── 结构化数据转换
│   └── 报告生成工具
└── 存储管理层
    ├── 文件系统管理
    ├── 版本控制系统
    ├── 数据库存储
    └── 云端备份
```

### 1.2 工具链特点
- **集成化**: 所有工具通过统一接口调用
- **自动化**: 支持批量操作和脚本化执行
- **可扩展**: 支持自定义工具和插件
- **可追溯**: 所有操作都有完整的日志记录

## 2. Playwright MCP工具详细使用

### 2.1 基础操作指南

#### 2.1.1 页面导航
```javascript
// 导航到目标页面
playwright_navigate_playwright({
  url: "https://app.huoban.com/navigations/3300000036684424/pages/7000000001717847",
  headless: false,
  width: 1920,
  height: 1080,
  timeout: 30000
})
```

#### 2.1.2 元素交互
```javascript
// 点击元素
playwright_click_playwright({
  selector: "text=登录"
})

// 填写表单
playwright_fill_playwright({
  selector: "input[placeholder='用户名']",
  value: "15313656268"
})

// 选择下拉框
playwright_select_playwright({
  selector: "select[name='role']",
  value: "admin"
})
```

#### 2.1.3 数据提取
```javascript
// 获取可见文本
playwright_get_visible_text_playwright()

// 获取HTML结构
playwright_get_visible_html_playwright({
  cleanHtml: true,
  removeScripts: true,
  maxLength: 20000
})

// 截图保存
playwright_screenshot_playwright({
  name: "功能界面",
  fullPage: true,
  savePng: true,
  downloadsDir: "分析结果目录"
})
```

### 2.2 高级功能使用

#### 2.2.1 iframe操作
```javascript
// iframe内元素点击
playwright_iframe_click_playwright({
  iframeSelector: "iframe[name='content']",
  selector: "button.submit"
})

// iframe内表单填写
playwright_iframe_fill_playwright({
  iframeSelector: "iframe[name='form']",
  selector: "input[name='data']",
  value: "测试数据"
})
```

#### 2.2.2 文件操作
```javascript
// 文件上传
playwright_upload_file_playwright({
  selector: "input[type='file']",
  filePath: "D:\\test\\sample.xlsx"
})

// 页面保存为PDF
playwright_save_as_pdf_playwright({
  outputPath: "分析结果目录",
  filename: "页面内容.pdf",
  format: "A4",
  printBackground: true
})
```

#### 2.2.3 网络监控
```javascript
// 监控HTTP响应
playwright_expect_response_playwright({
  id: "api_call_1",
  url: "**/api/data**"
})

// 验证响应内容
playwright_assert_response_playwright({
  id: "api_call_1",
  value: "success"
})
```

## 3. 标准分析流程

### 3.1 功能发现流程
```
1. 系统登录
   ├── 导航到登录页面
   ├── 输入登录凭据
   ├── 处理验证码/安全检查
   └── 确认登录成功

2. 界面遍历
   ├── 截图记录主界面
   ├── 识别所有功能入口
   ├── 记录导航结构
   └── 建立功能清单

3. 功能测试
   ├── 逐个点击功能入口
   ├── 记录功能界面
   ├── 测试基本操作
   └── 记录操作结果

4. 数据采集
   ├── 提取界面文本内容
   ├── 分析HTML结构
   ├── 记录交互行为
   └── 保存分析数据
```

### 3.2 权限分析流程
```
1. 角色识别
   ├── 分析用户角色类型
   ├── 记录角色权限差异
   ├── 测试权限边界
   └── 建立权限矩阵

2. 访问控制测试
   ├── 测试不同角色访问权限
   ├── 验证功能可见性
   ├── 检查操作权限
   └── 记录访问控制规则

3. 安全机制分析
   ├── 识别安全验证点
   ├── 分析认证机制
   ├── 测试会话管理
   └── 记录安全策略
```

## 4. 数据采集最佳实践

### 4.1 截图采集规范
1. **命名规范**
   - 格式: `功能模块_具体功能_时间戳.png`
   - 示例: `用户管理_用户列表_2025-06-27T11-33-57.png`

2. **质量要求**
   - 分辨率: 1920x1080或更高
   - 格式: PNG (保证清晰度)
   - 内容: 完整功能界面，避免截取不完整

3. **存储组织**
   - 按功能模块分类存储
   - 建立索引文件记录截图内容
   - 定期清理重复或无效截图

### 4.2 文本数据提取
1. **提取策略**
   - 优先提取结构化文本
   - 保留原始格式和层次
   - 过滤无关的装饰性文本

2. **数据清洗**
   - 去除HTML标签和样式
   - 统一换行符和空格
   - 处理特殊字符和编码

3. **结构化存储**
   - 使用JSON格式存储结构化数据
   - 建立统一的数据模式
   - 支持数据查询和分析

### 4.3 操作记录规范
1. **操作日志格式**
   ```json
   {
     "timestamp": "2025-06-27T11:33:57.100Z",
     "action": "click",
     "target": "button.login",
     "context": "登录页面",
     "result": "success",
     "screenshot": "login_success_2025-06-27T11-33-57.png"
   }
   ```

2. **关键信息记录**
   - 操作时间和序列
   - 操作对象和方法
   - 操作结果和状态变化
   - 相关截图和证据

## 5. 质量控制与验证

### 5.1 数据质量检查
1. **完整性检查**
   - [ ] 所有功能都有对应截图
   - [ ] 操作流程记录完整
   - [ ] 关键数据已提取保存

2. **准确性验证**
   - [ ] 截图内容清晰可读
   - [ ] 文本提取准确无误
   - [ ] 操作记录真实有效

3. **一致性校验**
   - [ ] 命名规范统一执行
   - [ ] 数据格式标准一致
   - [ ] 存储结构规范统一

### 5.2 工具性能监控
1. **响应时间监控**
   - 记录每个操作的执行时间
   - 识别性能瓶颈和优化点
   - 建立性能基准和告警

2. **错误率统计**
   - 记录工具操作失败情况
   - 分析失败原因和模式
   - 建立错误处理和重试机制

## 6. 故障排除指南

### 6.1 常见问题及解决方案
1. **页面加载超时**
   - 检查网络连接状态
   - 增加超时时间设置
   - 使用重试机制

2. **元素定位失败**
   - 检查选择器语法
   - 等待元素加载完成
   - 使用更稳定的定位方式

3. **截图保存失败**
   - 检查目录权限
   - 确认磁盘空间充足
   - 验证文件路径正确

### 6.2 调试技巧
1. **启用详细日志**
   - 开启工具调试模式
   - 记录详细操作日志
   - 分析错误堆栈信息

2. **分步调试**
   - 将复杂操作分解为简单步骤
   - 逐步验证每个操作
   - 定位具体问题点

## 7. 工具扩展与定制

### 7.1 自定义脚本开发
1. **脚本模板**
   ```javascript
   // 功能分析脚本模板
   async function analyzeFunction(functionName, entrySelector) {
     // 1. 导航到功能入口
     await playwright_click_playwright({selector: entrySelector});
     
     // 2. 截图记录
     await playwright_screenshot_playwright({
       name: `${functionName}_界面`,
       fullPage: true,
       savePng: true
     });
     
     // 3. 提取内容
     const content = await playwright_get_visible_text_playwright();
     
     // 4. 分析和记录
     return {
       function: functionName,
       content: content,
       timestamp: new Date().toISOString()
     };
   }
   ```

2. **批量操作脚本**
   - 支持批量功能遍历
   - 自动化数据采集
   - 结果汇总和报告生成

### 7.2 工具集成扩展
1. **第三方工具集成**
   - 图像识别工具
   - 文本分析工具
   - 数据可视化工具

2. **API接口开发**
   - 提供标准化API接口
   - 支持外部工具调用
   - 实现工具链互操作

---

**工具状态**: ✅ 已配置完成并验证可用
**使用建议**: 严格按照本指南操作，确保数据质量
**技术支持**: 工程工具专家提供技术支持
**更新计划**: 根据使用情况持续优化和更新
